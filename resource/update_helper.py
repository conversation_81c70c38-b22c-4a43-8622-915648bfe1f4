import re
import json
from typing import Dict, Any

UPDATE_API_URL = "http://localhost:8888/api/update_slot"

def update_patterns_to_es(compiled_patterns: Dict[str, re.Pattern], data_type: str, api_url: str = UPDATE_API_URL):
    """
    将编译后的正则模式更新到ES

    Args:
        api_url: 更新接口的URL，默认为本地服务
    """
    try:
        import requests
        print("开始将正则模式更新到ES...")

        # 准备更新数据
        update_data = []

        if data_type == "tool_regexs":
            for pattern_id, pattern in compiled_patterns.items():
                prefix, tool_name = pattern_id.split('-', 1)
                # 添加到更新列表
                update_data.append({
                    "slot_name": prefix,
                    "slot_value": tool_name,
                    "pattern_str": pattern.pattern,
                    "data_type": "tool_regexs"
                })
        elif data_type == "regex":
            for pattern_id, pattern in compiled_patterns.items():
                slot_value, slot_name = pattern_id.split("-", 1)
                # 添加到更新列表
                update_data.append({
                    "slot_name": slot_name,
                    "slot_value": slot_value,
                    "pattern_str": pattern.pattern,
                    "data_type": "regexs"
                })
        elif data_type == "keywords":
            for pattern_id, pattern in compiled_patterns.items():
                slot_value, slot_name = pattern_id.split("-", 1)
                # 添加到更新列表
                update_data.append({
                    "slot_name": slot_name,
                    "slot_value": slot_value,
                    "pattern_str": pattern.pattern,
                    "data_type": "keywords"
                })

        if not update_data:
            print("没有需要更新的数据")
            return

        # 构建请求数据
        request_data = {
            "update_data": update_data
        }

        # 发送请求
        response = requests.post(api_url, json=request_data)

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            print(f"更新成功: {result['message']}")
        else:
            print(f"更新失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"更新正则模式到ES时发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())


def update_constants_to_es(constants: Dict[str, Any], data_type: str, api_url: str = UPDATE_API_URL):
    """
    将常量数据更新到ES

    Args:
        constants: 常量数据字典
        data_type: 数据类型，如 "tool_constants"
        api_url: 更新接口的URL，默认为本地服务
    """
    try:
        import requests
        print("开始将常量数据更新到ES...")

        # 准备更新数据
        update_data = []

        for constant_id, constant_value in constants.items():
            prefix, constant_name = constant_id.split('-', 1)

            # 将常量值序列化为JSON字符串
            if isinstance(constant_value, (dict, list)):
                constant_str = json.dumps(constant_value, ensure_ascii=False)
            else:
                constant_str = str(constant_value)

            # 添加到更新列表
            update_data.append({
                "slot_name": prefix,
                "slot_value": constant_name,
                "pattern_str": constant_str,
                "data_type": data_type
            })

        if not update_data:
            print("没有需要更新的常量数据")
            return

        # 构建请求数据
        request_data = {
            "update_data": update_data
        }

        # 发送请求
        response = requests.post(api_url, json=request_data)

        # 检查响应
        if response.status_code == 200:
            result = response.json()
            print(f"常量数据更新成功: {result['message']}")
        else:
            print(f"常量数据更新失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"更新常量数据到ES时发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
