#!/usr/bin/env python3
"""
测试常量数据更新的脚本
"""

import json
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, '/matrix/0-Work/0_dev/slots_extra')

def _convert_tuples_to_lists(obj):
    """
    递归地将对象中的所有tuple转换为list，以便JSON序列化
    """
    if isinstance(obj, tuple):
        return list(obj)
    elif isinstance(obj, dict):
        return {key: _convert_tuples_to_lists(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [_convert_tuples_to_lists(item) for item in obj]
    else:
        return obj

def test_year_span_config():
    """测试年份跨度配置的序列化"""
    print("=== 测试年份跨度配置 ===")
    
    # 模拟 YEAR_SPAN_CONFIG 数据
    year_span_config = {
        'century': {
            "early": (0, 19),
            "middle": (20, 79),
            "late": (80, 99),
            "full": (0, 99)
        },
        'decade': {
            "early": (0, 2),
            "middle": (3, 6),
            "late": (7, 9),
            "full": (0, 9)
        }
    }
    
    print("原始配置:")
    print(year_span_config)
    
    # 转换 tuple 为 list
    converted = _convert_tuples_to_lists(year_span_config)
    print("\n转换后配置:")
    print(converted)
    
    # 测试 JSON 序列化
    try:
        json_str = json.dumps(converted, ensure_ascii=False)
        print("\n✅ JSON序列化成功!")
        print(f"JSON字符串: {json_str}")
        return True
    except Exception as e:
        print(f"\n❌ JSON序列化失败: {e}")
        return False

def test_solar_terms_data():
    """测试节气数据的序列化"""
    print("\n=== 测试节气数据 ===")
    
    # 模拟节气数据（包含tuple）
    solar_terms = {
        '小寒': [6.11, '1', (2019, -1), (1982, 1)],
        '大寒': [20.84, '1', (2082, 1)],
        '立春': [4.6295, '2', (None, 0)],
        '雨水': [19.4599, '2', (2026, -1)]
    }
    
    print("原始节气数据:")
    print(solar_terms)
    
    # 转换 tuple 为 list
    converted = _convert_tuples_to_lists(solar_terms)
    print("\n转换后节气数据:")
    print(converted)
    
    # 测试 JSON 序列化
    try:
        json_str = json.dumps(converted, ensure_ascii=False)
        print("\n✅ JSON序列化成功!")
        print(f"JSON字符串: {json_str}")
        return True
    except Exception as e:
        print(f"\n❌ JSON序列化失败: {e}")
        return False

def test_update_constants_function():
    """测试更新常量函数的逻辑"""
    print("\n=== 测试更新常量函数逻辑 ===")
    
    # 模拟常量数据
    test_constants = {
        'relative_time_range-year_span_config': {
            'century': {
                "early": (0, 19),
                "middle": (20, 79)
            }
        },
        'relative_time_range-solar_terms': {
            '小寒': [6.11, '1', (2019, -1)],
            '大寒': [20.84, '1', (2082, 1)]
        }
    }
    
    print("模拟常量数据:")
    for key, value in test_constants.items():
        print(f"  {key}: {value}")
    
    # 模拟更新逻辑
    update_data = []
    
    for constant_id, constant_value in test_constants.items():
        prefix, constant_name = constant_id.split('-', 1)
        
        # 将常量值序列化为JSON字符串
        if isinstance(constant_value, (dict, list)):
            # 先转换tuple为list，再序列化
            converted_value = _convert_tuples_to_lists(constant_value)
            try:
                constant_str = json.dumps(converted_value, ensure_ascii=False)
                print(f"\n✅ {constant_name} 序列化成功")
                print(f"   JSON: {constant_str}")
            except Exception as e:
                print(f"\n❌ {constant_name} 序列化失败: {e}")
                return False
        else:
            constant_str = str(constant_value)
        
        # 添加到更新列表
        update_data.append({
            "slot_name": prefix,
            "slot_value": constant_name,
            "pattern_str": constant_str,
            "data_type": "tool_constants"
        })
    
    print(f"\n✅ 成功处理 {len(update_data)} 个常量")
    return True

if __name__ == "__main__":
    print("开始测试常量数据更新...")
    
    # 测试年份跨度配置
    success1 = test_year_span_config()
    
    # 测试节气数据
    success2 = test_solar_terms_data()
    
    # 测试更新函数逻辑
    success3 = test_update_constants_function()
    
    if success1 and success2 and success3:
        print("\n🎉 所有测试通过！常量数据更新修复成功。")
        print("现在可以安全地运行常量数据更新到ES了。")
    else:
        print("\n❌ 部分测试失败，需要进一步检查。")
