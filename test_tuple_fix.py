#!/usr/bin/env python3
"""
测试 tuple 转换修复的脚本
"""

import json
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, '/matrix/0-Work/0_dev/slots_extra')

def _convert_tuples_to_lists(obj):
    """
    递归地将对象中的所有tuple转换为list，以便JSON序列化
    
    Args:
        obj: 要转换的对象
        
    Returns:
        转换后的对象
    """
    if isinstance(obj, tuple):
        return list(obj)
    elif isinstance(obj, dict):
        return {key: _convert_tuples_to_lists(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [_convert_tuples_to_lists(item) for item in obj]
    else:
        return obj

def test_tuple_conversion():
    """测试 tuple 转换功能"""
    print("=== 测试 tuple 转换功能 ===")
    
    # 测试数据，包含tuple
    test_data = {
        'century': {
            'early': (0, 19),
            'middle': (20, 79),
            'late': (80, 99),
            'full': (0, 99)
        },
        'decade': {
            'early': (0, 2),
            'middle': (3, 6),
            'late': (7, 9),
            'full': (0, 9)
        }
    }

    print('原始数据:')
    print(test_data)

    print('\n转换后的数据:')
    converted = _convert_tuples_to_lists(test_data)
    print(converted)

    print('\nJSON序列化测试:')
    try:
        json_str = json.dumps(converted, ensure_ascii=False)
        print('序列化成功!')
        print(f'JSON字符串: {json_str}')
        return True
    except Exception as e:
        print(f'序列化失败: {e}')
        return False

def test_time_constants():
    """测试时间常量数据"""
    print("\n=== 测试时间常量数据 ===")
    
    try:
        from resource.regex_patterns.time_relative_patterns import TIME_RELATIVE_CONSTANTS
        
        print(f"常量数据总数: {len(TIME_RELATIVE_CONSTANTS)}")
        
        # 测试包含 tuple 的常量
        year_span_key = 'relative_time_range-year_span_config'
        if year_span_key in TIME_RELATIVE_CONSTANTS:
            year_span_config = TIME_RELATIVE_CONSTANTS[year_span_key]
            print(f"\n年份跨度配置: {year_span_config}")
            
            # 转换并序列化
            converted = _convert_tuples_to_lists(year_span_config)
            json_str = json.dumps(converted, ensure_ascii=False)
            print(f"转换后的JSON: {json_str}")
            return True
        else:
            print(f"未找到 {year_span_key}")
            return False
            
    except Exception as e:
        print(f"测试时间常量失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试 tuple 转换修复...")
    
    # 测试基本转换功能
    success1 = test_tuple_conversion()
    
    # 测试实际的时间常量数据
    success2 = test_time_constants()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！tuple 转换修复成功。")
    else:
        print("\n❌ 测试失败，需要进一步检查。")
